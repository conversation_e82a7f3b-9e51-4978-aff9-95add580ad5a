<?xml version="1.0" encoding="UTF-8"?>
<web-app xmlns="http://xmlns.jcp.org/xml/ns/javaee"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://xmlns.jcp.org/xml/ns/javaee
         http://xmlns.jcp.org/xml/ns/javaee/web-app_4_0.xsd"
         version="4.0">

  <display-name>E-Learning Platform</display-name>

  <!-- 设置默认编码 -->
  <filter>
    <filter-name>CharacterEncodingFilter</filter-name>
    <filter-class>com.elearning.filter.CharacterEncodingFilter</filter-class>
    <init-param>
      <param-name>encoding</param-name>
      <param-value>UTF-8</param-value>
    </init-param>
  </filter>

  <filter-mapping>
    <filter-name>CharacterEncodingFilter</filter-name>
    <url-pattern>/*</url-pattern>
  </filter-mapping>

  <!-- Servlet配置 -->
  <servlet>
    <servlet-name>LoginServlet</servlet-name>
    <servlet-class>com.elearning.servlet.LoginServlet</servlet-class>
  </servlet>

  <servlet-mapping>
    <servlet-name>LoginServlet</servlet-name>
    <url-pattern>/login</url-pattern>
  </servlet-mapping>

  <servlet>
    <servlet-name>LogoutServlet</servlet-name>
    <servlet-class>com.elearning.servlet.LogoutServlet</servlet-class>
  </servlet>

  <servlet-mapping>
    <servlet-name>LogoutServlet</servlet-name>
    <url-pattern>/logout</url-pattern>
  </servlet-mapping>

  <servlet>
    <servlet-name>UserManagementServlet</servlet-name>
    <servlet-class>com.elearning.servlet.UserManagementServlet</servlet-class>
  </servlet>

  <servlet-mapping>
    <servlet-name>UserManagementServlet</servlet-name>
    <url-pattern>/admin/userManagement</url-pattern>
  </servlet-mapping>

  <servlet>
    <servlet-name>CourseManagementServlet</servlet-name>
    <servlet-class>com.elearning.servlet.CourseManagementServlet</servlet-class>
  </servlet>

  <servlet-mapping>
    <servlet-name>CourseManagementServlet</servlet-name>
    <url-pattern>/teacher/courseManagement</url-pattern>
  </servlet-mapping>

  <!-- 欢迎页面 -->
  <welcome-file-list>
    <welcome-file>index.jsp</welcome-file>
  </welcome-file-list>

  <!-- Session超时设置（30分钟） -->
  <session-config>
    <session-timeout>30</session-timeout>
  </session-config>

  <!-- 错误页面配置 -->
  <error-page>
    <error-code>404</error-code>
    <location>/error/404.jsp</location>
  </error-page>

  <error-page>
    <error-code>500</error-code>
    <location>/error/500.jsp</location>
  </error-page>

  <!-- MIME类型配置 -->
  <mime-mapping>
    <extension>css</extension>
    <mime-type>text/css</mime-type>
  </mime-mapping>

  <mime-mapping>
    <extension>js</extension>
    <mime-type>application/javascript</mime-type>
  </mime-mapping>

</web-app>
